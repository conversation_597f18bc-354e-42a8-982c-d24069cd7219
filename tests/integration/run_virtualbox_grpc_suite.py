#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 VirtualBox gRPC Test Suite Runner

Comprehensive test suite for VirtualBox + Vagrant + gRPC integration.
This runner orchestrates all VirtualBox-related tests including:
- VirtualBox installation verification
- Vagrant provider configuration
- gRPC communication on host port 40000
- Windows VM provisioning and management
- File injection via gRPC
- Complete Winamp analysis workflow

Usage:
    python tests/integration/run_virtualbox_grpc_suite.py
    python tests/integration/run_virtualbox_grpc_suite.py --quick
    python tests/integration/run_virtualbox_grpc_suite.py --winamp-only
"""

import argparse
import asyncio
import json
import subprocess
import sys
import time
from pathlib import Path

from rich.console import Console
from rich.panel import Panel
from rich.table import Table

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import test modules
from tests.integration.test_virtualbox_grpc_host import VirtualBoxGRPCTester


class VirtualBoxGRPCTestSuite:
    """VirtualBox gRPC test suite orchestrator."""

    def __init__(self, args):
        self.console = Console()
        self.args = args
        self.test_results = []
        
        # Test configuration
        self.tests_to_run = self._determine_tests_to_run()

    def _determine_tests_to_run(self) -> list:
        """Determine which tests to run based on arguments."""
        if self.args.winamp_only:
            return ["winamp_analysis"]
        elif self.args.quick:
            return ["prerequisites", "basic_grpc", "vm_creation"]
        else:
            return [
                "prerequisites",
                "basic_grpc", 
                "vm_creation",
                "file_injection",
                "winamp_analysis"
            ]

    def print_suite_header(self):
        """Print test suite header."""
        header_panel = Panel(
            "🖥️ [bold magenta]💩🎉TurdParty🎉💩 VirtualBox gRPC Test Suite[/bold magenta] 🖥️\n\n"
            "🎯 Comprehensive VirtualBox + Vagrant + gRPC Integration Testing\n"
            "🔗 gRPC Communication: localhost:40000\n"
            "🖥️ VM Provider: VirtualBox with Windows 10\n"
            "📊 Test Coverage: Prerequisites → VM → gRPC → Injection → Analysis\n\n"
            f"🧪 Tests to run: {', '.join(self.tests_to_run)}",
            title="VirtualBox gRPC Integration Test Suite",
            border_style="magenta",
        )
        self.console.print(header_panel)
        self.console.print()

    async def run_prerequisites_test(self) -> bool:
        """Test 1: Prerequisites verification."""
        self.console.print("[bold blue]🔍 Test 1: Prerequisites Verification[/bold blue]")
        
        try:
            tester = VirtualBoxGRPCTester()
            result = tester.check_prerequisites()
            
            self.test_results.append({
                "test": "prerequisites",
                "success": result,
                "message": "All prerequisites satisfied" if result else "Prerequisites missing"
            })
            
            if result:
                self.console.print("[green]✅ Prerequisites test passed[/green]")
            else:
                self.console.print("[red]❌ Prerequisites test failed[/red]")
            
            return result
            
        except Exception as e:
            self.console.print(f"[red]❌ Prerequisites test error: {e}[/red]")
            self.test_results.append({
                "test": "prerequisites",
                "success": False,
                "message": f"Error: {e}"
            })
            return False

    async def run_basic_grpc_test(self) -> bool:
        """Test 2: Basic gRPC connectivity."""
        self.console.print("[bold blue]🔗 Test 2: Basic gRPC Connectivity[/bold blue]")
        
        try:
            # Run the existing gRPC connectivity test
            result = subprocess.run([
                sys.executable, "scripts/test-vagrant-grpc-connectivity.py"
            ], capture_output=True, text=True, timeout=60)
            
            success = result.returncode == 0 and "READY FOR GRPC" in result.stdout
            
            self.test_results.append({
                "test": "basic_grpc",
                "success": success,
                "message": "gRPC connectivity verified" if success else "gRPC connectivity failed"
            })
            
            if success:
                self.console.print("[green]✅ Basic gRPC test passed[/green]")
            else:
                self.console.print("[red]❌ Basic gRPC test failed[/red]")
                self.console.print(f"[yellow]Output: {result.stdout}[/yellow]")
                self.console.print(f"[yellow]Error: {result.stderr}[/yellow]")
            
            return success
            
        except Exception as e:
            self.console.print(f"[red]❌ Basic gRPC test error: {e}[/red]")
            self.test_results.append({
                "test": "basic_grpc",
                "success": False,
                "message": f"Error: {e}"
            })
            return False

    async def run_vm_creation_test(self) -> bool:
        """Test 3: VirtualBox VM creation."""
        self.console.print("[bold blue]🖥️ Test 3: VirtualBox VM Creation[/bold blue]")
        
        try:
            tester = VirtualBoxGRPCTester()
            
            # Setup test file first
            if not tester.setup_test_file():
                raise Exception("Test file setup failed")
            
            # Test VM creation
            result = tester.test_virtualbox_vm_creation()
            
            self.test_results.append({
                "test": "vm_creation",
                "success": result,
                "message": "VM creation successful" if result else "VM creation failed"
            })
            
            if result:
                self.console.print("[green]✅ VM creation test passed[/green]")
            else:
                self.console.print("[red]❌ VM creation test failed[/red]")
            
            # Cleanup
            tester.cleanup_resources()
            
            return result
            
        except Exception as e:
            self.console.print(f"[red]❌ VM creation test error: {e}[/red]")
            self.test_results.append({
                "test": "vm_creation",
                "success": False,
                "message": f"Error: {e}"
            })
            return False

    async def run_file_injection_test(self) -> bool:
        """Test 4: File injection via gRPC."""
        self.console.print("[bold blue]💉 Test 4: File Injection via gRPC[/bold blue]")
        
        try:
            tester = VirtualBoxGRPCTester()
            result = tester.run_complete_test_suite()
            
            self.test_results.append({
                "test": "file_injection",
                "success": result,
                "message": "File injection successful" if result else "File injection failed"
            })
            
            if result:
                self.console.print("[green]✅ File injection test passed[/green]")
            else:
                self.console.print("[red]❌ File injection test failed[/red]")
            
            return result
            
        except Exception as e:
            self.console.print(f"[red]❌ File injection test error: {e}[/red]")
            self.test_results.append({
                "test": "file_injection",
                "success": False,
                "message": f"Error: {e}"
            })
            return False

    async def run_winamp_analysis_test(self) -> bool:
        """Test 5: Complete Winamp analysis workflow."""
        self.console.print("[bold blue]🎵 Test 5: Winamp Analysis Workflow[/bold blue]")
        
        try:
            # Run the Winamp analysis script with VirtualBox
            result = subprocess.run([
                sys.executable, "scripts/run-winamp-analysis.py"
            ], capture_output=True, text=True, timeout=1800)  # 30 minute timeout
            
            success = result.returncode == 0 and "completed successfully" in result.stdout
            
            self.test_results.append({
                "test": "winamp_analysis",
                "success": success,
                "message": "Winamp analysis completed" if success else "Winamp analysis failed"
            })
            
            if success:
                self.console.print("[green]✅ Winamp analysis test passed[/green]")
            else:
                self.console.print("[red]❌ Winamp analysis test failed[/red]")
                self.console.print(f"[yellow]Output: {result.stdout[-500:]}[/yellow]")  # Last 500 chars
                self.console.print(f"[yellow]Error: {result.stderr[-500:]}[/yellow]")
            
            return success
            
        except Exception as e:
            self.console.print(f"[red]❌ Winamp analysis test error: {e}[/red]")
            self.test_results.append({
                "test": "winamp_analysis",
                "success": False,
                "message": f"Error: {e}"
            })
            return False

    def generate_test_report(self):
        """Generate comprehensive test report."""
        self.console.print("\n[bold green]📊 VirtualBox gRPC Test Suite Report[/bold green]")
        
        # Summary table
        table = Table(title="🖥️ Test Results Summary")
        table.add_column("Test", style="cyan")
        table.add_column("Status", style="bold")
        table.add_column("Message", style="white")
        
        passed_tests = 0
        total_tests = len(self.test_results)
        
        for result in self.test_results:
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            status_style = "green" if result["success"] else "red"
            
            table.add_row(
                result["test"].replace("_", " ").title(),
                f"[{status_style}]{status}[/{status_style}]",
                result["message"]
            )
            
            if result["success"]:
                passed_tests += 1
        
        self.console.print(table)
        
        # Overall summary
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        summary_panel = Panel(
            f"📊 [bold]Test Summary[/bold]\n\n"
            f"Total Tests: {total_tests}\n"
            f"Passed: {passed_tests}\n"
            f"Failed: {total_tests - passed_tests}\n"
            f"Success Rate: {success_rate:.1f}%\n\n"
            f"{'🎉 All tests passed!' if passed_tests == total_tests else '⚠️ Some tests failed'}",
            title="VirtualBox gRPC Test Suite Results",
            border_style="green" if passed_tests == total_tests else "yellow",
        )
        self.console.print(summary_panel)
        
        # Save detailed report
        report_file = f"/tmp/virtualbox_grpc_test_report_{int(time.time())}.json"
        report_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S UTC", time.gmtime()),
            "test_suite": "VirtualBox gRPC Integration",
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": success_rate,
            "test_results": self.test_results,
            "configuration": {
                "tests_run": self.tests_to_run,
                "quick_mode": self.args.quick,
                "winamp_only": self.args.winamp_only
            }
        }
        
        with open(report_file, "w") as f:
            json.dump(report_data, f, indent=2)
        
        self.console.print(f"[cyan]📄 Detailed report saved to: {report_file}[/cyan]")
        
        return passed_tests == total_tests

    async def run_complete_suite(self) -> bool:
        """Run the complete VirtualBox gRPC test suite."""
        start_time = time.time()
        
        self.print_suite_header()
        
        try:
            # Run tests based on configuration
            all_passed = True
            
            if "prerequisites" in self.tests_to_run:
                if not await self.run_prerequisites_test():
                    all_passed = False
                    if not self.args.continue_on_failure:
                        self.console.print("[red]❌ Stopping due to prerequisites failure[/red]")
                        return False
            
            if "basic_grpc" in self.tests_to_run:
                if not await self.run_basic_grpc_test():
                    all_passed = False
                    if not self.args.continue_on_failure:
                        self.console.print("[red]❌ Stopping due to gRPC failure[/red]")
                        return False
            
            if "vm_creation" in self.tests_to_run:
                if not await self.run_vm_creation_test():
                    all_passed = False
                    if not self.args.continue_on_failure:
                        self.console.print("[red]❌ Stopping due to VM creation failure[/red]")
                        return False
            
            if "file_injection" in self.tests_to_run:
                if not await self.run_file_injection_test():
                    all_passed = False
                    if not self.args.continue_on_failure:
                        self.console.print("[red]❌ Stopping due to file injection failure[/red]")
                        return False
            
            if "winamp_analysis" in self.tests_to_run:
                if not await self.run_winamp_analysis_test():
                    all_passed = False
            
            # Generate final report
            total_time = time.time() - start_time
            self.console.print(f"\n⏱️ Total test suite time: {total_time:.2f} seconds")
            
            return self.generate_test_report()
            
        except Exception as e:
            self.console.print(f"[red]❌ Test suite error: {e}[/red]")
            return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="💩🎉TurdParty🎉💩 VirtualBox gRPC Test Suite"
    )
    parser.add_argument(
        "--quick",
        action="store_true",
        help="Run quick tests only (prerequisites, gRPC, VM creation)"
    )
    parser.add_argument(
        "--winamp-only",
        action="store_true",
        help="Run only the Winamp analysis test"
    )
    parser.add_argument(
        "--continue-on-failure",
        action="store_true",
        help="Continue running tests even if some fail"
    )

    args = parser.parse_args()

    # Run the test suite
    suite = VirtualBoxGRPCTestSuite(args)

    try:
        success = asyncio.run(suite.run_complete_suite())

        if success:
            print(f"\n🎉 VirtualBox gRPC test suite completed successfully!")
            return 0
        else:
            print(f"\n❌ VirtualBox gRPC test suite failed!")
            return 1

    except KeyboardInterrupt:
        print("\n⚠️ Test suite interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
